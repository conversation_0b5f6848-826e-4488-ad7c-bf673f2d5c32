import React from "react";
import { Box, Typography, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";

const PriceLegend = ({ min, avg, max, priceMode, isMobile }) => {
  const theme = useTheme();
  const isResponsiveMobile = useMediaQuery(theme.breakpoints.down("md"));

  // Use prop if provided, otherwise fall back to responsive detection
  const mobile = isMobile !== undefined ? isMobile : isResponsiveMobile;

  // Set the unit label
  const unit = priceMode === "perSqft" ? "AED/sqft" : "AED";

  return (
    <Box
      sx={{
        position: "absolute",
        bottom: mobile ? 20 : 6,
        left: mobile ? 20 : "auto",
        right: mobile ? "auto" : 10,
        backgroundColor: "#1e1e1e",
        color: "white",
        borderRadius: 2,
        padding: mobile ? "8px 10px" : "12px 16px",
        zIndex: 998,
        boxShadow: "0 2px 6px rgba(0,0,0,0.35)",
        fontSize: mobile ? 11 : 13,
        minWidth: mobile ? 160 : 200,
        lineHeight: 1.4,
      }}
    >
      <Typography
        variant="subtitle2"
        sx={{
          fontSize: mobile ? 11.5 : 13.5,
          fontWeight: 600,
          marginBottom: 1,
        }}
      >
        Legend ({priceMode === "perSqft" ? "Per Sq.Ft" : "By Value"})
      </Typography>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
        <Typography variant="body2">
          <span style={{ color: "#00ff00", fontWeight: 700 }}>●</span> Low:{" "}
          {min?.toLocaleString()} {unit}
        </Typography>
        <Typography variant="body2">
          <span style={{ color: "#ffff00", fontWeight: 700 }}>●</span> Avg:{" "}
          {avg?.toLocaleString()} {unit}
        </Typography>
        <Typography variant="body2">
          <span style={{ color: "#ff007f", fontWeight: 700 }}>●</span> High:{" "}
          {max?.toLocaleString()} {unit}
        </Typography>
      </Box>
    </Box>
  );
};

export default PriceLegend;
