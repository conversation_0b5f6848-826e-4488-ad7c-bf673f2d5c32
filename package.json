{"name": "gamma-mapbox", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@googleapis/sheets": "^9.8.0", "@mapbox/mapbox-gl-geocoder": "^5.0.3", "@mui/icons-material": "^6.4.11", "@mui/material": "^6.4.11", "@mui/x-date-pickers": "^8.4.0", "@turf/boolean-point-in-polygon": "^7.2.0", "@turf/centroid": "^7.2.0", "@turf/turf": "^7.2.0", "axios": "^1.9.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^5.1.0", "fuse.js": "^7.1.0", "googleapis": "^149.0.0", "mapbox-gl": "^3.12.0", "mapbox-gl-geocoder": "^2.0.1", "node-fetch": "^3.3.2", "react": "^18.3.1", "react-autosuggest": "^10.1.0", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "nodemon": "^3.1.10", "vite": "^6.3.5"}}