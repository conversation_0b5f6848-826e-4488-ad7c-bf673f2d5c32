import React, { useEffect, useRef } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import booleanPointInPolygon from "@turf/boolean-point-in-polygon";
import centroid from "@turf/centroid";

mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_TOKEN;

const ClusterMap = ({
  data,
  flyToLocation,
  onPriceStatsUpdate,
  basemap,
  geojsonData,
  priceMode,
  showGeojsonLayer,
}) => {
  const mapRef = useRef(null);
  const containerRef = useRef(null);

  const getPolygonStats = (polygons, points, mode) => {
    const polygonData = polygons.features.map((poly) => {
      const id = poly.properties.id || poly.properties.name || Math.random();
      const matches = points.filter((pt) => booleanPointInPolygon(pt, poly));

      const prices = matches
        .map((pt) => {
          const sold = Number(pt.properties?.soldFor || 0);
          const sqft =
            parseFloat(pt.properties?.sqft?.replace(/[^\d.]/g, "")) || 1;
          return mode === "perSqft" ? sold / sqft : sold;
        })
        .filter(Boolean);

      const count = prices.length;
      const avg =
        count > 0
          ? Math.round(prices.reduce((a, b) => a + b, 0) / count)
          : null;

      let center = [0, 0];
      try {
        center = centroid(poly).geometry.coordinates;
      } catch (e) {
        console.warn("Invalid centroid for polygon:", id);
      }

      return {
        id,
        count,
        avg,
        center,
        color:
          count === 0
            ? "rgba(0,0,0,0)"
            : `rgba(144, 238, 144, ${
                0.2 + 0.6 * (count / Math.max(1, points.length))
              })`,
      };
    });

    const colorMap = {};
    const pointsForLabels = [];

    polygonData.forEach(({ id, count, color, avg, center }) => {
      colorMap[id] = color;
      if (avg !== null) {
        pointsForLabels.push({
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: center,
          },
          properties: {
            id,
            label:
              priceMode === "perSqft"
                ? `${avg} AED/sqft`
                : `AED ${avg.toLocaleString()}`,
          },
        });
      }
    });

    return { colorMap, avgLabels: pointsForLabels };
  };

  useEffect(() => {
    if (!containerRef.current) return;

    if (mapRef.current && typeof mapRef.current.remove === "function") {
      try {
        mapRef.current.remove();
      } catch (e) {
        console.warn("Map cleanup error:", e);
      }
    }

    const map = new mapboxgl.Map({
      container: containerRef.current,
      style: basemap || "mapbox://styles/mapbox/dark-v10",
      center: [55.22, 25.18],
      zoom: 10,
      attributionControl: false,
    });

    mapRef.current = map;

    const updateClustersAndLegend = () => {
      const features = map.querySourceFeatures("properties", {
        filter: ["!", ["has", "point_count"]],
      });

      const visiblePrices = features
        .map((f) => {
          const sold = Number(f.properties?.soldFor);
          const sqft =
            parseFloat(f.properties?.sqft?.replace(/[^\d.]/g, "")) || 1;
          return priceMode === "perSqft" ? sold / sqft : sold;
        })
        .filter((p) => !isNaN(p) && p > 0);

      if (visiblePrices.length === 0) {
        // No visible data: reset legend to 0s
        onPriceStatsUpdate?.({ min: 0, avg: 0, max: 0 });
        // Optionally, reset color scale to neutral (gray)
        map.setPaintProperty("unclustered-point", "circle-color", "#999");
        return;
      }

      const min = Math.min(...visiblePrices);
      const max = Math.max(...visiblePrices);
      const avg = Math.round(
        visiblePrices.reduce((a, b) => a + b, 0) / visiblePrices.length
      );

      onPriceStatsUpdate?.({ min, avg, max });

      // Update unclustered-point coloring dynamically
      map.setPaintProperty("unclustered-point", "circle-color", [
        "interpolate",
        ["linear"],
        ["get", priceMode === "perSqft" ? "pricePerSqft" : "soldFor"],
        min,
        "#00ff00",
        avg,
        "#ffff00",
        max,
        "#ff007f",
      ]);
    };

    map.on("load", () => {
      const validData = data.filter(
        (d) =>
          d.lng !== undefined &&
          d.lat !== undefined &&
          !isNaN(Number(d.lng)) &&
          !isNaN(Number(d.lat))
      );

      const clusterFeatures = validData.map((item) => {
        const sold = Number(item.soldFor) || 0;
        const sqft = parseFloat(item.sqft?.replace(/[^\d.]/g, "")) || 1;
        const pricePerSqft = sold / sqft;
        const price = priceMode === "perSqft" ? pricePerSqft : sold;

        return {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [Number(item.lng), Number(item.lat)],
          },
          properties: {
            ...item,
            soldFor: sold,
            sqft: item.sqft,
            price,
            pricePerSqft,
          },
        };
      });

      map.addSource("properties", {
        type: "geojson",
        data: {
          type: "FeatureCollection",
          features: clusterFeatures,
        },
        cluster: true,
        clusterMaxZoom: 14,
        clusterRadius: 50,
      });

      map.addLayer({
        id: "clusters",
        type: "circle",
        source: "properties",
        filter: ["has", "point_count"],
        paint: {
          "circle-color": [
            "step",
            ["get", "point_count"],
            "#00BCD4", // small clusters
            100,
            "#ffa726",
            750,
            "#ef5350", // largest clusters
          ],
          "circle-radius": [
            "step",
            ["get", "point_count"],
            15,
            100,
            20,
            750,
            25,
          ],
          "circle-opacity": 0.7,
        },
        layout: {
          visibility: showGeojsonLayer ? "none" : "visible",
        },
      });

      map.addLayer({
        id: "cluster-count",
        type: "symbol",
        source: "properties",
        filter: ["has", "point_count"],
        layout: {
          "text-field": "{point_count_abbreviated}",
          "text-size": 12,
          visibility: showGeojsonLayer ? "none" : "visible",
        },
        paint: {
          "text-color": "#ffffff",
        },
      });

      map.addLayer({
        id: "unclustered-point",
        type: "circle",
        source: "properties",
        filter: ["!", ["has", "point_count"]],
        paint: {
          "circle-color": "#999", // will be overridden later
          "circle-radius": 6,
          "circle-stroke-width": 1,
          "circle-stroke-color": "#fff",
        },
        layout: {
          visibility: showGeojsonLayer ? "none" : "visible",
        },
      });

      map.on("moveend", updateClustersAndLegend);
      updateClustersAndLegend();

      map.on("click", "clusters", (e) => {
        const features = map.queryRenderedFeatures(e.point, {
          layers: ["clusters"],
        });
        const clusterId = features[0].properties.cluster_id;
        map
          .getSource("properties")
          .getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (!err) {
              map.easeTo({
                center: features[0].geometry.coordinates,
                zoom,
              });
            }
          });
      });

      map.on("click", "unclustered-point", (e) => {
        const coords = e.features[0]?.geometry?.coordinates;
        const props = e.features[0]?.properties;

        if (coords && props) {
          const sold = Number(props.soldFor);
          const sqft = parseFloat(props.sqft?.replace(/[^\d.]/g, "")) || 1;
          const perSqft = Math.round(sold / sqft);
          const developer = props.developer || props["Developer"] || "N/A";
          const listingUrl =
            props["Link to Listing"] ||
            props.link ||
            props.listingUrl ||
            props["link_to_listing"];

          const popupHTML = `
            <div style="font-size: 13px; line-height: 1.4; max-width: 260px;">
              ${
                props.image
                  ? `<img src="${props.image}" alt="Property" style="width:100%;max-width:220px;border-radius:8px;margin-bottom:8px;object-fit:cover;" />`
                  : ""
              }
              <strong>${props.propertyType}</strong><br/>
              <strong>Total:</strong> AED ${sold.toLocaleString()}<br/>
              <strong>Sqft:</strong> ${props.sqft}<br/>
              <strong>Per Sq.Ft:</strong> AED ${perSqft}/sqft<br/>
              <strong>Developer:</strong> ${developer}<br/>
              <strong>Location:</strong> ${props.location}<br/>
              <strong>Date:</strong> ${props.date}<br/>
              ${
                props.comments
                  ? `<strong>Details:</strong> ${props.comments}<br/>`
                  : ""
              }
              ${
                listingUrl
                  ? `<a href="${listingUrl}" target="_blank" style="color:#29b6f6;text-decoration:underline;"> View Listing</a>`
                  : ""
              }
            </div>
          `;

          new mapboxgl.Popup({ offset: 25 })
            .setLngLat(coords)
            .setHTML(popupHTML)
            .addTo(map);
        }
      });

      // Handle GeoJSON overlays
      if (geojsonData) {
        const enrichedPoints = clusterFeatures;
        const { colorMap, avgLabels } = getPolygonStats(
          geojsonData,
          enrichedPoints,
          priceMode
        );

        const updatedGeoJSON = {
          ...geojsonData,
          features: geojsonData.features.map((f) => ({
            ...f,
            properties: {
              ...f.properties,
              _colorKey: f.properties.id || f.properties.name || Math.random(),
            },
          })),
        };

        map.addSource("geojson-layer", {
          type: "geojson",
          data: updatedGeoJSON,
        });

        map.addLayer({
          id: "geojson-layer",
          type: "fill",
          source: "geojson-layer",
          paint: {
            "fill-color": [
              "match",
              ["get", "_colorKey"],
              ...Object.entries(colorMap).flat(),
              "rgba(0,0,0,0)",
            ],
            "fill-opacity": 1,
          },
          layout: {
            visibility: showGeojsonLayer ? "visible" : "none",
          },
        });

        map.addLayer({
          id: "geojson-boundaries",
          type: "line",
          source: "geojson-layer",
          paint: {
            "line-color": "#ffffff",
            "line-width": 0.01,
            "line-opacity": 0.8,
          },
          layout: {
            visibility: showGeojsonLayer ? "visible" : "none",
          },
        });

        map.addSource("polygon-labels", {
          type: "geojson",
          data: {
            type: "FeatureCollection",
            features: avgLabels,
          },
        });

        map.addLayer({
          id: "polygon-label-layer",
          type: "symbol",
          source: "polygon-labels",
          layout: {
            "text-field": ["get", "label"],
            "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
            "text-size": 12,
            "text-offset": [0, 0.5],
            visibility: showGeojsonLayer ? "visible" : "none",
          },
          paint: {
            "text-color": "#ffffff",
          },
        });
      }
    });

    return () => {
      if (mapRef.current) {
        try {
          mapRef.current.remove();
        } catch (e) {
          console.warn("Error cleaning up map:", e);
        }
      }
    };
  }, [data, geojsonData, priceMode, showGeojsonLayer]);

  useEffect(() => {
    if (mapRef.current && basemap) {
      try {
        mapRef.current.setStyle(basemap);
      } catch (e) {
        // setStyle may fail if map is not ready
      }
    }
  }, [basemap]);

  useEffect(() => {
    if (flyToLocation && mapRef.current) {
      mapRef.current.flyTo({
        center: [flyToLocation.lng, flyToLocation.lat],
        zoom: 14,
        speed: 1.2,
      });
    }
  }, [flyToLocation]);

  return <div ref={containerRef} style={{ width: "100%", height: "100%" }} />;
};

export default ClusterMap;
