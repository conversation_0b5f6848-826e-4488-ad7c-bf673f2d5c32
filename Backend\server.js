import express from 'express';
import cors from 'cors';
import fetch from 'node-fetch';
import fs from 'fs/promises';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// ✅ Enable CORS for all origins (localhost + production frontend)
app.use(cors());

// ✅ Serve static files (so frontend can access /gamma_property.json)
app.use(express.static('.'));

// === Config ===
const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;
const GOOGLE_SHEET_ID = process.env.GOOGLE_SHEET_ID;
const RANGE = 'Sheet1';

const CACHE_PATH = './gamma_property.json';
const CHECKSUM_PATH = './last_checksum.txt';

// === Utils ===
function generateChecksum(str) {
  return crypto.createHash('md5').update(str).digest('hex');
}

function dmsToDecimal(dms) {
  const [deg, min, secDir] = dms.split(/[°']/);
  const sec = parseFloat(secDir.replace(/[^\d.]/g, ''));
  const dir = secDir.replace(/[\d.]/g, '');
  let decimal = parseInt(deg) + parseInt(min) / 60 + sec / 3600;
  if (dir === 'S' || dir === 'W') decimal *= -1;
  return decimal;
}

function convertToGeoJSON(rows) {
  const headers = rows[0];
  const features = rows.slice(1).map((row) => {
    const properties = {};
    headers.forEach((header, index) => {
      properties[header] = row[index] || '';
    });

    const coordinateStr = properties['Coordinates'] || '';
    const match = coordinateStr.match(/(\d+°\d+'[\d.]+")N\s+(\d+°\d+'[\d.]+")E/);

    let geometry = null;
    if (match) {
      const lat = dmsToDecimal(match[1]);
      const lon = dmsToDecimal(match[2]);
      geometry = {
        type: 'Point',
        coordinates: [lon, lat],
      };
    }

    return {
      type: 'Feature',
      geometry,
      properties,
    };
  });

  return {
    type: 'FeatureCollection',
    features,
  };
}

async function fetchSheetData() {
  const url = `https://sheets.googleapis.com/v4/spreadsheets/${GOOGLE_SHEET_ID}/values/${RANGE}?key=${GOOGLE_API_KEY}`;
  const res = await fetch(url);
  const data = await res.json();
  return data;
}

// === API Route for Google Places ===
app.get('/api/places-autocomplete', async (req, res) => {
  const { input, lat, lng } = req.query;

  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${input}&key=${GOOGLE_API_KEY}&location=${lat},${lng}&radius=50000`
    );
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching Google Places autocomplete:', error);
    res.status(500).json({ error: 'Failed to fetch suggestions' });
  }
});

// === API Route for Property Data ===
app.get('/api/gamma-property', async (req, res) => {
  try {
    const sheet = await fetchSheetData();
    const rows = sheet.values;
    const sheetString = JSON.stringify(rows);
    const currentChecksum = generateChecksum(sheetString);

    let lastChecksum = '';
    try {
      lastChecksum = await fs.readFile(CHECKSUM_PATH, 'utf-8');
    } catch {}

    if (currentChecksum !== lastChecksum) {
      console.log('✅ Sheet updated. Sending fresh data...');
      const geojson = convertToGeoJSON(rows);
      await fs.writeFile(CACHE_PATH, JSON.stringify(geojson));
      await fs.writeFile(CHECKSUM_PATH, currentChecksum);
      return res.json({ updated: true, data: geojson });
    } else {
      console.log('♻️ No change. Returning not-updated flag.');
      return res.json({ updated: false });
    }
  } catch (err) {
    console.error('❌ Error:', err.message);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// === Start Server ===
app.listen(PORT, () => {
  console.log(`🚀 Server running at http://localhost:${PORT}`);
});
