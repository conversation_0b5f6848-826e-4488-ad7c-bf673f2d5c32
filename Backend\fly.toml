# fly.toml app configuration file generated for dubai-estate-nldvnw on 2025-06-12T06:11:26Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'dubai-estate-nldvnw'
primary_region = 'cdg'

[build]

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
 cpu_kind = "shared"
cpus = 1
memory = "256mb"
memory_mb = 256
