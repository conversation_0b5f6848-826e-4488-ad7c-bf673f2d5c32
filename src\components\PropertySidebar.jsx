import React, { useMemo, useState } from 'react';
import { Box, Typography, Chip, Select, MenuItem, FormControl, InputLabel } from '@mui/material';

const currency = (n) =>
  typeof n === 'number' && !isNaN(n) ? `AED ${n.toLocaleString()}` : 'AED —';

const ratingBadgeColor = (r) => {
  if (r >= 9) return '#1565c0';
  if (r >= 8) return '#1e88e5';
  if (r >= 7) return '#42a5f5';
  return '#607d8b';
};

const PropertyCard = ({ item, priceMode, onSelect }) => {
  const sold = Number(item.soldFor) || 0;
  const sqft = parseFloat(item.sqft?.toString().replace(/[^\d.]/g, '')) || 1;
  const perSqft = Math.round(sold / sqft);

  const priceText = priceMode === 'perSqft'
    ? `${perSqft.toLocaleString()} AED/sqft`
    : currency(sold);

  return (
    <Box
      onClick={() => onSelect?.(item)}
      sx={{
        display: 'flex',
        gap: 1.5,
        p: 1.25,
        borderRadius: 2,
        bgcolor: '#0f0f0f',
        border: '1px solid #222',
        cursor: 'pointer',
        transition: 'background .15s, transform .06s',
        '&:hover': { bgcolor: '#141414' },
        '&:active': { transform: 'scale(0.998)' }
      }}
    >
      <Box
        sx={{
          width: 92,
          height: 92,
          borderRadius: 1.5,
          overflow: 'hidden',
          flexShrink: 0,
          bgcolor: '#111',
          border: '1px solid #222'
        }}
      >
        <img
          src={item.image || '/placeholder.jpg'}
          alt={item.location || item.propertyType || 'Property'}
          style={{ width: '100%', height: '100%', objectFit: 'cover', display: 'block' }}
          loading="lazy"
        />
      </Box>

      <Box sx={{ minWidth: 0, display: 'flex', flexDirection: 'column', gap: 0.5 }}>
        <Typography
          variant="subtitle2"
          sx={{
            color: '#fff',
            fontWeight: 700,
            lineHeight: 1.2,
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}
          title={item.location || item.propertyType}
        >
          {item.location || item.propertyType || '—'}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
          {item.rating ? (
            <Chip
              label={Number(item.rating).toFixed(1)}
              size="small"
              sx={{
                bgcolor: ratingBadgeColor(Number(item.rating)),
                color: '#fff',
                fontWeight: 700
              }}
            />
          ) : null}

          {item.propertyType ? (
            <Typography variant="caption" sx={{ color: '#ccc' }}>
              {item.propertyType}
            </Typography>
          ) : null}
        </Box>

        <Typography variant="body2" sx={{ color: '#ffd54f', fontWeight: 700 }}>
          {priceText}
        </Typography>

        <Typography
          variant="caption"
          sx={{
            color: '#9e9e9e',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}
          title={item.specs || item.comments || ''}
        >
          {item.specs || item.comments || ''}
        </Typography>
      </Box>
    </Box>
  );
};


const PropertySidebar = ({ items = [], priceMode = 'value', onSelect, open = false, onClose }) => {
  const [sortMode, setSortMode] = useState('priceHighLow');

  const sorted = useMemo(() => {
    let arr = [...items];
    switch (sortMode) {
      case 'priceHighLow':
        arr.sort((a, b) => (b.soldFor || 0) - (a.soldFor || 0));
        break;
      case 'priceLowHigh':
        arr.sort((a, b) => (a.soldFor || 0) - (b.soldFor || 0));
        break;
      case 'ratingHighLow':
        arr.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      case 'ratingLowHigh':
        arr.sort((a, b) => (a.rating || 0) - (b.rating || 0));
        break;
      default:
        break;
    }
    return arr;
  }, [items, sortMode]);

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: open ? 320 : -400, // 320px is FilterBar width
        bottom: 0,
        width: 360,
        maxWidth: 'calc(100vw - 24px)',
        bgcolor: '#0b0b0b',
        borderRadius: 2,
        border: '1px solid #222',
        boxShadow: '0 6px 20px rgba(0,0,0,0.35)',
        overflow: 'hidden',
        zIndex: 1291,
        display: 'flex',
        flexDirection: 'column',
        transition: 'left 0.08s cubic-bezier(.7,.2,.3,1)',
      }}
    >
      <Box
        sx={{
          px: 2,
          py: 1.25,
          borderBottom: '1px solid #222',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          bgcolor: '#0e0e0e',
          position: 'relative',
        }}
      >
        <Typography variant="subtitle1" sx={{ color: '#fff', fontWeight: 800 }}>
          Results
        </Typography>
        <Typography variant="caption" sx={{ color: '#bbb' }}>
          {sorted.length.toLocaleString()} properties
        </Typography>
      </Box>

      {/* Sort dropdown */}
      <Box sx={{ px: 2, py: 1, borderBottom: '1px solid #222', bgcolor: '#101010' }}>
        <FormControl fullWidth size="small">
          <InputLabel sx={{ color: '#ccc', fontSize: 13 }}>Sort By</InputLabel>
          <Select
            value={sortMode}
            label="Sort By"
            onChange={e => setSortMode(e.target.value)}
            sx={{ color: '#ccc', fontSize: 13, bgcolor: '#20232b', borderRadius: 1 }}
            MenuProps={{ PaperProps: { sx: { bgcolor: '#111', color: '#ccc' } } }}
          >
            <MenuItem value="priceHighLow">Price: High to Low</MenuItem>
            <MenuItem value="priceLowHigh">Price: Low to High</MenuItem>
            <MenuItem value="ratingHighLow">Rating: High to Low</MenuItem>
            <MenuItem value="ratingLowHigh">Rating: Low to High</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Box
        sx={{
          flex: 1,
          overflowY: 'auto',
          p: 1.25,
          display: 'grid',
          gap: 1.25,
          '&::-webkit-scrollbar': { width: '8px', background: '#000' },
          '&::-webkit-scrollbar-thumb': { background: '#222', borderRadius: 6 },
          '&::-webkit-scrollbar-track': { background: '#000' },
        }}
      >
        {sorted.map((item, idx) => (
          <PropertyCard key={idx} item={item} priceMode={priceMode} onSelect={onSelect} />
        ))}
      </Box>
    </Box>
  );
};

export default PropertySidebar;
