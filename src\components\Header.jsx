import React, { useState } from 'react';
import {
  AppBar, Toolbar, Box, Collapse,
  FormControl, InputLabel, Select, MenuItem, useMediaQuery
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import SearchBar from './SearchBar';
import FilterBar from './FilterBar';

const Header = ({
  searchValue,
  onSearchChange,
  locationOptions,
  onLocationSelect,
  propertyTypes,
  extractedTags,
  filters,
  onApplyFilters,
  priceMode,
  basemap,
  onBasemapChange
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [filterOpen, setFilterOpen] = useState(false);

  return (
    <AppBar
      position="fixed"
      color="default"
      elevation={2}
      sx={{
        backgroundColor: '#1e1e1e',
        zIndex: 1201,
        px: 1,
        py: 1
      }}
    >
      <Box sx={{ width: '100%' }}>
        <Too<PERSON><PERSON> disableGutters sx={{ minHeight: 'unset', width: '100%' }}>
          {isMobile ? (
            // ------------------ MOBILE ------------------
            <Box sx={{ width: '100%' }}>
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                <img src="/2.svg" alt="Logo" style={{ height: 44 }} />
                <Box sx={{ flexGrow: 1 }}>
                  <SearchBar
                    value={searchValue}
                    onChange={onSearchChange}
                    onSelect={onLocationSelect}
                    options={locationOptions}
                    compact
                  />
                </Box>
                <Box sx={{ minWidth: 100 }}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: '#ccc' }}>Basemap</InputLabel>
                    <Select
                      value={basemap}
                      onChange={(e) => onBasemapChange(e.target.value)}
                      label="Basemap"
                      sx={{ color: '#fff' }}
                    >
                      <MenuItem value="mapbox://styles/mapbox/dark-v10">Dark</MenuItem>
                      <MenuItem value="mapbox://styles/mapbox/light-v10">Light</MenuItem>
                      <MenuItem value="mapbox://styles/mapbox/satellite-v9">Satellite</MenuItem>
                      
                    </Select>
                  </FormControl>
                </Box>
              </Box>

              <Box mt={1}>
                <Box
                  onClick={() => setFilterOpen((prev) => !prev)}
                  sx={{
                    fontSize: 13,
                    px: 2,
                    py: 1,
                    textAlign: 'center',
                    color: '#00ffcc',
                    border: '1px solid #00ffcc',
                    borderRadius: 1,
                    cursor: 'pointer'
                  }}
                >
                  {filterOpen ? 'Hide Filters' : 'Show Filters'}
                </Box>
              </Box>

              <Collapse in={filterOpen}>
                <FilterBar
                  types={propertyTypes}
                  tags={extractedTags}
                  initialFilters={filters}
                  onApply={onApplyFilters}
                  compact
                  priceMode={priceMode}
                />
              </Collapse>
            </Box>
          ) : (
            // ------------------ DESKTOP ------------------
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: 1,
                flexWrap: 'wrap',
                width: '100%'
              }}
            >
              {/* Logo */}
              <Box sx={{ flex: '0 0 auto' }}>
                <img src="/2.svg" alt="Logo" style={{ height: 40 }} />
              </Box>

              {/* SearchBar */}
              <Box sx={{ flex: '1 1 150px', minWidth: 120, maxWidth: 200 }}>
                <SearchBar
                  value={searchValue}
                  onChange={onSearchChange}
                  onSelect={onLocationSelect}
                  options={locationOptions}
                  compact
                />
              </Box>

              {/* FilterBar */}
              <Box sx={{ flex: '4 1 600px', minWidth: 300 }}>
                <FilterBar
                  types={propertyTypes}
                  tags={extractedTags}
                  initialFilters={filters}
                  onApply={onApplyFilters}
                  compact
                  priceMode={priceMode}
                />
              </Box>

              {/* Basemap Selector */}
              <Box
  sx={{
    flex: '0 1 140px',
    minWidth: 100,
    maxWidth: 160,
  }}
>

                <FormControl fullWidth size="small">
                  <InputLabel sx={{ color: '#ccc' }}>Basemap</InputLabel>
                  <Select
                    value={basemap}
                    fullWidth
                    onChange={(e) => onBasemapChange(e.target.value)}
                    label="Basemap"
                    sx={{ color: '#fff' }}
                    MenuProps={{
                      PaperProps: {
                        sx: { zIndex: 15000 }
                      }
                    }}
                  >
                    <MenuItem value="mapbox://styles/mapbox/dark-v10">Dark</MenuItem>
                    <MenuItem value="mapbox://styles/mapbox/light-v10">Light</MenuItem>
                    <MenuItem value="mapbox://styles/mapbox/satellite-v9">Satellite</MenuItem>
                    
                  </Select>
                </FormControl>
              </Box>
            </Box>
          )}
        </Toolbar>
      </Box>
    </AppBar>
  );
};

export default Header;
