import React, { useState } from "react";
import { TextField, Autocomplete } from "@mui/material";

const SearchBar = ({
  value,
  onChange,
  onSelect,
  options = [],
  compact,
  setFlyToLocation,
}) => {
  const [inputValue, setInputValue] = useState(value);

  const handleInputChange = (event, newInputValue) => {
    setInputValue(newInputValue);
    onChange(newInputValue);
  };

  const handlePlaceSelect = (event, selectedLocation) => {
    if (!selectedLocation) return;

    const selected = options.find((loc) => loc.label === selectedLocation);
    if (selected && selected.lat && selected.lng) {
      setFlyToLocation({ lat: selected.lat, lng: selected.lng });
      onSelect?.(selectedLocation);
    }
  };

  return (
    <Autocomplete
      freeSolo
      value={inputValue}
      onInputChange={handleInputChange}
      onChange={handlePlaceSelect}
      options={options.map((loc) => loc.label)}
      ListboxProps={{
        sx: {
          backgroundColor: "#111",
          color: "#ccc",
          maxHeight: 250,
          overflowY: "auto",
          "@media (min-width: 768px)": {
            "&::-webkit-scrollbar": {
              width: "6px",
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "#000",
              borderRadius: "4px",
            },
            "&::-webkit-scrollbar-track": {
              background: "transparent",
            },
          },
        },
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder="Search location..."
          size="small"
          sx={{
            bgcolor: "#23272f",
            borderRadius: 2,
            input: { color: "#fff" },
            "& .MuiOutlinedInput-root": {
              backgroundColor: "#131518",
              "& fieldset": { borderColor: "#333" },
              "&:hover fieldset": { borderColor: "#555" },
              "&.Mui-focused fieldset": { borderColor: "#00bfa5" },
            },
          }}
        />
      )}
    />
  );
};

export default SearchBar;
