const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const fs = require('fs').promises;
const crypto = require('crypto');
const dotenv = require('dotenv');

dotenv.config();

const app = express();
const PORT = process.env.PORT || 8080;

app.use(cors());
app.use(express.static('.'));

const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;
const GOOGLE_SHEET_ID = process.env.GOOGLE_SHEET_ID;
const RANGE = 'Sheet1';
const CACHE_PATH = './gamma_property.json';
const CHECKSUM_PATH = './last_checksum.txt';

// === Utils ===
function generateChecksum(str) {
  return crypto.createHash('md5').update(str).digest('hex');
}

function dmsToDecimal(dms) {
  const match = dms.match(/(\d+)°(\d+)'([\d.]+)"?([NSEW])/i);
  if (!match) return null;

  const deg = parseInt(match[1]);
  const min = parseInt(match[2]);
  const sec = parseFloat(match[3]);
  const dir = match[4].toUpperCase();

  let decimal = deg + min / 60 + sec / 3600;
  if (dir === 'S' || dir === 'W') decimal *= -1;

  return decimal;
}

function convertToGeoJSON(rows) {
  const headers = rows[0];
  const features = [];

  rows.slice(1).forEach((row, index) => {
    const properties = {};
    headers.forEach((header, i) => {
      properties[header] = row[i] || '';
    });

    const coordinateStr = properties['Coordinates'] || '';
    let geometry = null;

    // Match DMS: 25°10'07.5"N 55°12'46.6"E
    const match = coordinateStr.match(/(\d+°\d+'[\d.]+)"?[NS]\s+(\d+°\d+'[\d.]+)"?[EW]/i);

    if (match) {
      const latMatch = coordinateStr.match(/(\d+°\d+'[\d.]+)"?([NS])/i);
      const lonMatch = coordinateStr.match(/(\d+°\d+'[\d.]+)"?([EW])/i);

      if (latMatch && lonMatch) {
        const lat = dmsToDecimal(`${latMatch[1]}"${latMatch[2]}`);
        const lon = dmsToDecimal(`${lonMatch[1]}"${lonMatch[2]}`);
        if (lat !== null && lon !== null) {
          geometry = {
            type: 'Point',
            coordinates: [lon, lat],
          };
        }
      }
    } else {
      // Fallback: decimal format
      const decimalParts = coordinateStr.split(',').map(p => parseFloat(p.trim()));
      if (decimalParts.length === 2 && !isNaN(decimalParts[0]) && !isNaN(decimalParts[1])) {
        geometry = {
          type: 'Point',
          coordinates: [decimalParts[1], decimalParts[0]], // [lon, lat]
        };
      } else {
        console.warn(`⚠️ Skipping invalid coordinate on row ${index + 2}:`, coordinateStr);
      }
    }

    features.push({
      type: 'Feature',
      geometry,
      properties,
    });
  });

  return {
    type: 'FeatureCollection',
    features,
  };
}

async function fetchSheetData() {
  const url = `https://sheets.googleapis.com/v4/spreadsheets/${GOOGLE_SHEET_ID}/values/${RANGE}?key=${GOOGLE_API_KEY}`;
  const res = await fetch(url);
  const data = await res.json();
  return data;
}

// === API Routes ===

app.get('/api/places-autocomplete', async (req, res) => {
  const { input, lat, lng } = req.query;

  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${input}&key=${GOOGLE_API_KEY}&location=${lat},${lng}&radius=50000`
    );
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching Google Places autocomplete:', error);
    res.status(500).json({ error: 'Failed to fetch suggestions' });
  }
});

app.get('/api/gamma-property', async (req, res) => {
  try {
    const sheet = await fetchSheetData();
    const rows = sheet.values;
    const sheetString = JSON.stringify(rows);
    const currentChecksum = generateChecksum(sheetString);

    let lastChecksum = '';
    try {
      lastChecksum = await fs.readFile(CHECKSUM_PATH, 'utf-8');
    } catch {}

    if (currentChecksum !== lastChecksum) {
      console.log('✅ Sheet updated. Sending fresh data...');
      const geojson = convertToGeoJSON(rows);
      await fs.writeFile(CACHE_PATH, JSON.stringify(geojson));
      await fs.writeFile(CHECKSUM_PATH, currentChecksum);
      return res.json({ updated: true, data: geojson });
    } else {
      console.log('♻️ No change. Returning not-updated flag.');
      return res.json({ updated: false });
    }
  } catch (err) {
    console.error('❌ Error:', err.message);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/', (req, res) => {
  res.send('✅ Dubai-Estate Backend is running!');
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Server running on port ${PORT}`);
});
